<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

        <title><?php echo e(config('app.name', 'Laravel')); ?></title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700&display=swap" rel="stylesheet" />

        <!-- Scripts -->
        <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    </head>
    <body class="font-sans antialiased bg-gradient-to-br from-indigo-50 via-white to-cyan-50">
        <div class="min-h-screen flex">
            <!-- Left side - Decorative -->
            <div class="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-indigo-600 via-purple-600 to-cyan-600 relative overflow-hidden">
                <div class="absolute inset-0 bg-black/20"></div>
                <div class="relative z-10 flex flex-col justify-center items-center text-white p-12">
                    <div class="max-w-md text-center">
                        <h1 class="text-4xl font-bold mb-6">Welcome to <?php echo e(config('app.name', 'Laravel')); ?></h1>
                        <p class="text-xl opacity-90 mb-8">Experience the power of modern web development with our elegant authentication system.</p>
                        <div class="flex justify-center space-x-4">
                            <div class="w-3 h-3 bg-white/30 rounded-full animate-pulse"></div>
                            <div class="w-3 h-3 bg-white/50 rounded-full animate-pulse" style="animation-delay: 0.2s"></div>
                            <div class="w-3 h-3 bg-white/70 rounded-full animate-pulse" style="animation-delay: 0.4s"></div>
                        </div>
                    </div>
                </div>
                <!-- Decorative elements -->
                <div class="absolute top-0 left-0 w-40 h-40 bg-white/10 rounded-full -translate-x-20 -translate-y-20"></div>
                <div class="absolute bottom-0 right-0 w-60 h-60 bg-white/10 rounded-full translate-x-20 translate-y-20"></div>
                <div class="absolute top-1/2 left-1/4 w-20 h-20 bg-white/5 rounded-full"></div>
            </div>

            <!-- Right side - Form -->
            <div class="flex-1 flex flex-col justify-center items-center p-6 sm:p-12">
                <div class="w-full max-w-md">
                    <!-- Logo -->
                    <div class="text-center mb-8">
                        <a href="/" class="inline-block">
                            <?php if (isset($component)) { $__componentOriginal8892e718f3d0d7a916180885c6f012e7 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8892e718f3d0d7a916180885c6f012e7 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.application-logo','data' => ['class' => 'w-16 h-16 fill-current text-indigo-600 hover:text-indigo-700 transition-colors duration-200']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('application-logo'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-16 h-16 fill-current text-indigo-600 hover:text-indigo-700 transition-colors duration-200']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8892e718f3d0d7a916180885c6f012e7)): ?>
<?php $attributes = $__attributesOriginal8892e718f3d0d7a916180885c6f012e7; ?>
<?php unset($__attributesOriginal8892e718f3d0d7a916180885c6f012e7); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8892e718f3d0d7a916180885c6f012e7)): ?>
<?php $component = $__componentOriginal8892e718f3d0d7a916180885c6f012e7; ?>
<?php unset($__componentOriginal8892e718f3d0d7a916180885c6f012e7); ?>
<?php endif; ?>
                        </a>
                        <h2 class="mt-4 text-2xl font-bold text-gray-900"><?php echo e(config('app.name', 'Laravel')); ?></h2>
                    </div>

                    <!-- Form Container -->
                    <div class="bg-white/80 backdrop-blur-sm shadow-xl rounded-2xl p-8 border border-white/20">
                        <?php echo e($slot); ?>

                    </div>

                    <!-- Footer -->
                    <div class="text-center mt-6">
                        <p class="text-sm text-gray-500">
                            © <?php echo e(date('Y')); ?> <?php echo e(config('app.name', 'Laravel')); ?>. All rights reserved.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </body>
</html>
<?php /**PATH D:\laravel-sessions\session-one\resources\views/layouts/guest.blade.php ENDPATH**/ ?>