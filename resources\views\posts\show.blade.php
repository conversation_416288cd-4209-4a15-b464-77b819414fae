<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ $post->title }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <div class="flex justify-between items-center mb-6">
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900 mb-2">{{ $post->title }}</h1>
                            <div class="flex items-center text-sm text-gray-500 space-x-4">
                                <span>By {{ $post->user->name }}</span>
                                <span>•</span>
                                <span>{{ $post->created_at->format('F j, Y') }}</span>
                                <span>•</span>
                                <span class="px-2 py-1 text-xs font-semibold rounded-full 
                                    {{ $post->status === 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                    {{ ucfirst($post->status) }}
                                </span>
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <a href="{{ route('posts.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                                Back to Posts
                            </a>
                            @if($post->user_id === auth()->id())
                                <a href="{{ route('posts.edit', $post) }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                    Edit Post
                                </a>
                            @endif
                        </div>
                    </div>

                    @if($post->excerpt)
                        <div class="bg-gray-50 border-l-4 border-blue-500 p-4 mb-6">
                            <p class="text-lg text-gray-700 italic">{{ $post->excerpt }}</p>
                        </div>
                    @endif

                    <div class="prose max-w-none text-gray-700 leading-relaxed">
                        <div class="whitespace-pre-wrap text-base">{{ $post->content }}</div>
                    </div>

                    @if($post->published_at)
                        <div class="mt-8 pt-6 border-t border-gray-200">
                            <p class="text-sm text-gray-500">
                                Published on {{ $post->published_at->format('F j, Y \a\t g:i A') }}
                            </p>
                        </div>
                    @endif

                    @if($post->user_id === auth()->id())
                        <div class="mt-8 pt-6 border-t border-gray-200">
                            <div class="flex justify-between items-center">
                                <p class="text-sm text-gray-500">Post Management</p>
                                <div class="flex space-x-2">
                                    <a href="{{ route('posts.edit', $post) }}" class="text-blue-600 hover:text-blue-900">Edit</a>
                                    <form action="{{ route('posts.destroy', $post) }}" method="POST" class="inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="text-red-600 hover:text-red-900" 
                                                onclick="return confirm('Are you sure you want to delete this post?')">
                                            Delete
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
