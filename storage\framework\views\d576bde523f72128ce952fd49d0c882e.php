<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            <?php echo e($post->title); ?>

        </h2>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <div class="flex justify-between items-center mb-6">
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900 mb-2"><?php echo e($post->title); ?></h1>
                            <div class="flex items-center text-sm text-gray-500 space-x-4">
                                <span>By <?php echo e($post->user->name); ?></span>
                                <span>•</span>
                                <span><?php echo e($post->created_at->format('F j, Y')); ?></span>
                                <span>•</span>
                                <span class="px-2 py-1 text-xs font-semibold rounded-full 
                                    <?php echo e($post->status === 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'); ?>">
                                    <?php echo e(ucfirst($post->status)); ?>

                                </span>
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <a href="<?php echo e(route('posts.index')); ?>" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                                Back to Posts
                            </a>
                            <?php if($post->user_id === auth()->id()): ?>
                                <a href="<?php echo e(route('posts.edit', $post)); ?>" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                    Edit Post
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>

                    <?php if($post->excerpt): ?>
                        <div class="bg-gray-50 border-l-4 border-blue-500 p-4 mb-6">
                            <p class="text-lg text-gray-700 italic"><?php echo e($post->excerpt); ?></p>
                        </div>
                    <?php endif; ?>

                    <div class="prose max-w-none text-gray-700 leading-relaxed">
                        <div class="whitespace-pre-wrap text-base"><?php echo e($post->content); ?></div>
                    </div>

                    <?php if($post->published_at): ?>
                        <div class="mt-8 pt-6 border-t border-gray-200">
                            <p class="text-sm text-gray-500">
                                Published on <?php echo e($post->published_at->format('F j, Y \a\t g:i A')); ?>

                            </p>
                        </div>
                    <?php endif; ?>

                    <?php if($post->user_id === auth()->id()): ?>
                        <div class="mt-8 pt-6 border-t border-gray-200">
                            <div class="flex justify-between items-center">
                                <p class="text-sm text-gray-500">Post Management</p>
                                <div class="flex space-x-2">
                                    <a href="<?php echo e(route('posts.edit', $post)); ?>" class="text-blue-600 hover:text-blue-900">Edit</a>
                                    <form action="<?php echo e(route('posts.destroy', $post)); ?>" method="POST" class="inline">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <button type="submit" class="text-red-600 hover:text-red-900" 
                                                onclick="return confirm('Are you sure you want to delete this post?')">
                                            Delete
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\laravel-sessions\session-one\resources\views/posts/show.blade.php ENDPATH**/ ?>