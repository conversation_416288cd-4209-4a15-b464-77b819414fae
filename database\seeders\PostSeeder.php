<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Post;
use App\Models\User;
use Illuminate\Support\Str;

class PostSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first user or create one
        $user = User::first();

        if (!$user) {
            $user = User::create([
                'name' => 'Demo User',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
            ]);
        }

        $posts = [
            [
                'title' => 'Welcome to Our Blog',
                'excerpt' => 'This is our first blog post. Welcome to our amazing blog where we share insights and stories.',
                'content' => "Welcome to our blog! We're excited to share our journey with you.\n\nThis is the beginning of something great. We'll be posting regular updates about our projects, insights from our team, and helpful tutorials.\n\nStay tuned for more amazing content!",
                'status' => 'published',
                'published_at' => now(),
            ],
            [
                'title' => 'Getting Started with <PERSON><PERSON>',
                'excerpt' => 'Learn the basics of Laravel framework and how to build amazing web applications.',
                'content' => "Laravel is an amazing PHP framework that makes web development enjoyable and creative.\n\nIn this post, we'll cover:\n- Setting up Laravel\n- Understanding MVC architecture\n- Creating your first routes\n- Working with databases\n\nLaravel's elegant syntax and powerful features make it perfect for both beginners and experienced developers.",
                'status' => 'published',
                'published_at' => now()->subDays(1),
            ],
            [
                'title' => 'Building Modern Web Applications',
                'excerpt' => 'Explore modern web development techniques and best practices.',
                'content' => "Modern web applications require a solid foundation and the right tools.\n\nKey considerations:\n- User experience design\n- Performance optimization\n- Security best practices\n- Scalable architecture\n\nWe'll dive deep into each of these topics in upcoming posts.",
                'status' => 'draft',
                'published_at' => null,
            ],
            [
                'title' => 'The Future of Web Development',
                'excerpt' => 'What trends and technologies will shape the future of web development.',
                'content' => "The web development landscape is constantly evolving.\n\nEmerging trends include:\n- Progressive Web Apps\n- Serverless architecture\n- AI integration\n- Enhanced security measures\n\nStaying updated with these trends is crucial for any developer.",
                'status' => 'published',
                'published_at' => now()->subDays(3),
            ],
        ];

        foreach ($posts as $postData) {
            Post::create([
                'title' => $postData['title'],
                'slug' => Str::slug($postData['title']),
                'excerpt' => $postData['excerpt'],
                'content' => $postData['content'],
                'status' => $postData['status'],
                'user_id' => $user->id,
                'published_at' => $postData['published_at'],
            ]);
        }
    }
}
