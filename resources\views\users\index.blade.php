<form method="POST" action="{{ route('users.store') }}" class="mt-6 space-y-6">    @csrf
        <div>
        <x-input-label for="name" :value="__('Name')" />        <x-text-input id="name" name="name" type="text" class="mt-1 block w-full" required autofocus />
        <x-input-error class="mt-2" :messages="$errors->get('name')" />    </div>
    <div>
        <x-input-label for="email" :value="__('Email')" />        <x-text-input id="email" name="email" type="email" class="mt-1 block w-full" required />
        <x-input-error class="mt-2" :messages="$errors->get('email')" />    </div>
    <div>
        <x-input-label for="password" :value="__('Password')" />        <x-text-input id="password" name="password" type="password" class="mt-1 block w-full" required />
        <x-input-error class="mt-2" :messages="$errors->get('password')" />    </div>
    <div class="flex items-center gap-4">
        <x-primary-button>{{ __('Save') }}</x-primary-button>
    </div>
</form>











